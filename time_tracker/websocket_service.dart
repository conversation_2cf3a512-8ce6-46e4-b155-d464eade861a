import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;
import '../models/app_model.dart';
import '../../core/constants/api_constants.dart';

class WebSocketService {
  WebSocketChannel? _channel;
  StreamSubscription? _channelSubscription;
  final String _wsUrl;

  final StreamController<TrackingStatus> _trackingStatusController = StreamController.broadcast();
  final StreamController<AppModel> _appUpdateController = StreamController.broadcast();
  final StreamController<Map<String, dynamic>> _sessionUpdateController = StreamController.broadcast();
  final StreamController<List<AppModel>> _appsListController = StreamController.broadcast();
  final StreamController<List<TimelineModel>> _timelineController = StreamController.broadcast();
  final StreamController<List<AppStatistics>> _statisticsController = StreamController.broadcast();
  final StreamController<Map<int, int>> _sessionCountsController = StreamController.broadcast();
  final StreamController<bool> _connectionStateController = StreamController.broadcast();
  final StreamController<List<CheckpointModel>> _checkpointsController = StreamController.broadcast();
  final StreamController<List<CheckpointWithStatus>> _checkpointsWithStatusController = StreamController.broadcast();
  final StreamController<CheckpointModel?> _activeCheckpointController = StreamController.broadcast();
  final StreamController<List<CheckpointDurationModel>> _checkpointStatsController = StreamController.broadcast();

  Stream<TrackingStatus> get trackingStatusStream => _trackingStatusController.stream;
  Stream<AppModel> get appUpdateStream => _appUpdateController.stream;
  Stream<Map<String, dynamic>> get sessionUpdateStream => _sessionUpdateController.stream;
  Stream<List<AppModel>> get appsListStream => _appsListController.stream;
  Stream<List<TimelineModel>> get timelineStream => _timelineController.stream;
  Stream<List<AppStatistics>> get statisticsStream => _statisticsController.stream;
  Stream<Map<int, int>> get sessionCountsStream => _sessionCountsController.stream;
  Stream<bool> get connectionStateStream => _connectionStateController.stream;
  Stream<List<CheckpointModel>> get checkpointsStream => _checkpointsController.stream;
  Stream<List<CheckpointWithStatus>> get checkpointsWithStatusStream => _checkpointsWithStatusController.stream;
  Stream<CheckpointModel?> get activeCheckpointStream => _activeCheckpointController.stream;
  Stream<List<CheckpointDurationModel>> get checkpointStatsStream => _checkpointStatsController.stream;

  bool _isConnected = false;
  bool _isConnecting = false;
  bool _isDisposed = false;
  Timer? _reconnectTimer;
  Timer? _heartbeatTimer;
  int _reconnectAttempts = 0;
  static const int maxReconnectAttempts = 10;
  static const Duration reconnectDelay = Duration(seconds: 2);
  static const Duration heartbeatInterval = Duration(seconds: 30);

  // Callback for when WebSocket fails permanently
  VoidCallback? _onConnectionFailed;

  WebSocketService({String? wsUrl}) : _wsUrl = wsUrl ?? ApiConstants.wsUrl;

  void setConnectionFailedCallback(VoidCallback callback) {
    _onConnectionFailed = callback;
  }

  bool get isConnected => _isConnected;

  Future<void> connect() async {
    if (_isDisposed || _isConnecting) return;

    _isConnecting = true;

    try {
      // Clean up any existing connection
      await _cleanupConnection();

      print('Attempting WebSocket connection to $_wsUrl');
      _channel = WebSocketChannel.connect(
        Uri.parse(_wsUrl),
        protocols: ['websocket'],
      );

      await _channel!.ready;

      // Set up the stream listener
      _channelSubscription = _channel!.stream.listen(
        _handleMessage,
        onError: _handleError,
        onDone: _handleDisconnection,
        cancelOnError: false,
      );

      _isConnected = true;
      _isConnecting = false;
      _reconnectAttempts = 0;

      // Emit connection state change
      if (!_connectionStateController.isClosed) {
        _connectionStateController.add(true);
      }

      _startHeartbeat();
      print('WebSocket connected to $_wsUrl');

      // Process any queued messages
      _processMessageQueue();

      // Request initial data
      _requestInitialData();
    } catch (e) {
      print('WebSocket connection failed: $e');
      _isConnected = false;
      _isConnecting = false;

      // Emit connection state change
      if (!_connectionStateController.isClosed) {
        _connectionStateController.add(false);
      }

      _scheduleReconnect();
    }
  }

  Future<void> _cleanupConnection() async {
    await _channelSubscription?.cancel();
    _channelSubscription = null;

    // Attempt to close the WebSocket channel and ignore any errors that might occur
    _channel?.sink.close().catchError((_) {}).then((_) {
      _channel = null;
    });

    _isConnected = false;

    // Emit connection state change
    if (!_connectionStateController.isClosed) {
      _connectionStateController.add(false);
    }

    _stopHeartbeat();
  }

  void _requestInitialData() {
    if (!_isConnected) return;

    // Request all initial data immediately - the message queue will handle ordering
    sendMessage('get_apps', {});
    sendMessage('get_tracking_status', {});
    sendMessage('get_session_counts', {});
    // Request the all-time timeline snapshot
    getTimeline();
  }

  void _handleMessage(dynamic message) {
    if (_isDisposed || !_isConnected) return;

    try {
      if (message == 'pong') {
        print('Received pong from server');
        return;
      }

      // Handle empty or invalid messages
      if (message == null || message.toString().trim().isEmpty) {
        print('Received empty message, ignoring');
        return;
      }

      // Log message length and first part for debugging
      final messageStr = message as String;
      print('Raw WebSocket message received (${messageStr.length} chars): ${messageStr.length > 200 ? messageStr.substring(0, 200) + "..." : messageStr}');

      // Try to parse the outer JSON structure
      Map<String, dynamic> data;
      try {
        data = json.decode(messageStr);
      } catch (e) {
        print('Error parsing outer JSON: $e');
        print('Raw message: $messageStr');
        return;
      }

      final messageType = data['type'] as String?;
      final payloadString = data['payload'] as String?;

      print('Parsed message - type: $messageType');
      if (payloadString != null) {
        print('Payload length: ${payloadString.length} chars');
      }

      if (payloadString == null && messageType != 'pong') {
        print('No payload found for message type: $messageType');
        return;
      }

      Map<String, dynamic>? payload;
      if (payloadString != null) {
        try {
          payload = json.decode(payloadString) as Map<String, dynamic>;
          print('Successfully parsed payload with ${payload.keys.length} keys');
        } catch (e) {
          print('Error parsing payload JSON: $e');
          print('Payload string: ${payloadString.length > 500 ? payloadString.substring(0, 500) + "..." : payloadString}');

          // If it's an error message, try to handle it differently
          if (messageType == 'error') {
            print('Server error message with malformed payload: $payloadString');
            return;
          }

          return;
        }
      }

      // Check if controllers are still open before adding events
      switch (messageType) {
        case 'tracking_status_update':
          print('Processing tracking_status_update');
          if (payload != null && payload['status'] != null && !_trackingStatusController.isClosed) {
            final statusData = payload['status'] as Map<String, dynamic>;
            final status = TrackingStatus.fromJson(_convertTrackingStatusFields(statusData));
            print('Emitting tracking status: ${status.isTracking}');
            _trackingStatusController.add(status);
          }
          break;

        case 'app_update':
          print('Processing app_update');
          if (payload != null && payload['app'] != null && !_appUpdateController.isClosed) {
            final appData = payload['app'] as Map<String, dynamic>;
            final app = AppModel.fromJson(_convertAppFields(appData));
            print('Emitting app update: ${app.name}');
            _appUpdateController.add(app);
          }
          break;

        case 'session_update':
          print('Processing session_update');
          if (payload != null && !_sessionUpdateController.isClosed) {
            print('Emitting session update');
            _sessionUpdateController.add(payload);
          }
          break;

        case 'apps_list':
          print('Processing apps_list');
          if (payload != null && payload['apps'] != null && !_appsListController.isClosed) {
            try {
              final appsList = (payload['apps'] as List)
                  .map((app) => AppModel.fromJson(_convertAppFields(app as Map<String, dynamic>)))
                  .toList();
              print('Successfully parsed ${appsList.length} apps');
              print('Emitting apps list with ${appsList.length} apps to stream');
              _appsListController.add(appsList);
              print('Apps list emitted to stream successfully');
            } catch (e) {
              print('Error processing apps list: $e');
              print('Apps payload: ${payload['apps']}');
            }
          } else {
            print('Failed to process apps_list - payload: ${payload != null}, apps key: ${payload?['apps'] != null}, controller closed: ${_appsListController.isClosed}');
          }
          break;

        case 'timeline_data':
          print('Processing timeline_data');
          if (payload != null && payload['timeline'] != null && !_timelineController.isClosed) {
            final timelineList = (payload['timeline'] as List)
                .map((item) => TimelineModel.fromJson(_convertTimelineFields(item as Map<String, dynamic>)))
                .toList();
            print('Emitting timeline with ${timelineList.length} entries');
            _timelineController.add(timelineList);
          }
          break;

        case 'statistics_data':
          print('Processing statistics_data');
          if (payload != null && payload['statistics'] != null && !_statisticsController.isClosed) {
            final statisticsList = (payload['statistics'] as List)
                .map((item) => AppStatistics.fromJson(_convertStatisticsFields(item as Map<String, dynamic>)))
                .toList();
            print('Emitting statistics with ${statisticsList.length} entries');
            _statisticsController.add(statisticsList);
          }
          break;

        case 'session_counts':
          print('Processing session_counts');
          if (payload != null && payload['counts'] != null && !_sessionCountsController.isClosed) {
            final countsData = payload['counts'] as List;
            final counts = <int, int>{};
            for (final count in countsData) {
              if (count is Map<String, dynamic>) {
                final appId = count['app_id'] as int?;
                final sessionCount = count['session_count'] as int?;
                if (appId != null && sessionCount != null) {
                  counts[appId] = sessionCount;
                }
              }
            }
            print('Emitting session counts: $counts');
            _sessionCountsController.add(counts);
          }
          break;

        case 'checkpoints_list':
          print('Processing checkpoints_list');
          if (payload != null && payload['checkpoints'] != null) {
            final checkpointsList = (payload['checkpoints'] as List)
                .map((item) => CheckpointWithStatus.fromJson(_convertCheckpointFields(item as Map<String, dynamic>)))
                .toList();
            print('Emitting checkpoints with ${checkpointsList.length} entries');

            // Emit CheckpointWithStatus stream
            if (!_checkpointsWithStatusController.isClosed) {
              _checkpointsWithStatusController.add(checkpointsList);
            }

            // Emit CheckpointModel stream for backward compatibility
            if (!_checkpointsController.isClosed) {
              _checkpointsController.add(checkpointsList.map((cws) => cws.checkpoint).toList());
            }

            // Extract and emit active checkpoint
            final activeCheckpoint = checkpointsList.where((cws) => cws.isActive).firstOrNull?.checkpoint;
            if (!_activeCheckpointController.isClosed) {
              _activeCheckpointController.add(activeCheckpoint);
            }
          }
          break;

        case 'checkpoint_created':
          print('Processing checkpoint_created');
          if (payload != null && payload['checkpoints'] != null) {
            final checkpointsList = (payload['checkpoints'] as List)
                .map((item) => CheckpointWithStatus.fromJson(_convertCheckpointFields(item as Map<String, dynamic>)))
                .toList();
            print('Emitting updated checkpoints after creation');

            // Emit CheckpointWithStatus stream
            if (!_checkpointsWithStatusController.isClosed) {
              _checkpointsWithStatusController.add(checkpointsList);
            }

            // Emit CheckpointModel stream for backward compatibility
            if (!_checkpointsController.isClosed) {
              _checkpointsController.add(checkpointsList.map((cws) => cws.checkpoint).toList());
            }

            // Extract and emit active checkpoint
            final activeCheckpoint = checkpointsList.where((cws) => cws.isActive).firstOrNull?.checkpoint;
            if (!_activeCheckpointController.isClosed) {
              _activeCheckpointController.add(activeCheckpoint);
            }
          }
          break;

        case 'checkpoint_update':
          print('Processing checkpoint_update');
          if (payload != null && payload['active_checkpoint_id'] != null && !_activeCheckpointController.isClosed) {
            // Request updated checkpoints list to get the new active checkpoint
            getCheckpoints(payload['app_id'] as int);
          }
          break;

        case 'checkpoint_deleted':
          print('Processing checkpoint_deleted');
          if (payload != null && payload['checkpoints'] != null) {
            final checkpointsList = (payload['checkpoints'] as List)
                .map((item) => CheckpointWithStatus.fromJson(_convertCheckpointFields(item as Map<String, dynamic>)))
                .toList();
            print('Emitting updated checkpoints after deletion');

            // Emit CheckpointWithStatus stream
            if (!_checkpointsWithStatusController.isClosed) {
              _checkpointsWithStatusController.add(checkpointsList);
            }

            // Emit CheckpointModel stream for backward compatibility
            if (!_checkpointsController.isClosed) {
              _checkpointsController.add(checkpointsList.map((cws) => cws.checkpoint).toList());
            }

            // Extract and emit active checkpoint
            final activeCheckpoint = checkpointsList.where((cws) => cws.isActive).firstOrNull?.checkpoint;
            if (!_activeCheckpointController.isClosed) {
              _activeCheckpointController.add(activeCheckpoint);
            }
          }
          break;

        case 'checkpoint_stats':
          print('Processing checkpoint_stats');
          if (payload != null && payload['durations'] != null && !_checkpointStatsController.isClosed) {
            final durationsList = (payload['durations'] as List)
                .map((item) => CheckpointDurationModel.fromJson(item as Map<String, dynamic>))
                .toList();
            print('Emitting checkpoint stats with ${durationsList.length} entries');
            _checkpointStatsController.add(durationsList);
          }
          break;

        case 'error':
          if (payload != null && payload['message'] != null) {
            print('Server error: ${payload['message']}');
          }
          break;

        case 'pong':
          print('Received pong from server');
          break;

        default:
          print('Unknown WebSocket message type: $messageType');
      }
    } catch (e, stackTrace) {
      print('Error parsing WebSocket message: $e');
      print('Stack trace: $stackTrace');
      print('Raw message length: ${(message as String).length}');

      // Emit connection state change
      if (!_connectionStateController.isClosed) {
        _connectionStateController.add(false);
      }
    }
  }

  Map<String, dynamic> _convertAppFields(Map<String, dynamic> serverData) {
    try {
      return {
        'id': serverData['id'],
        'name': serverData['name'],
        'productName': serverData['product_name'],
        'duration': serverData['duration'],
        'launches': serverData['launches'],
        'longestSession': serverData['longest_session'],
        'longestSessionOn': serverData.containsKey('longest_session_on')
            ? serverData['longest_session_on']
            : null,
      };
    } catch (e) {
      print('Error converting app fields: $e');
      print('Server data: $serverData');
      rethrow;
    }
  }

  Map<String, dynamic> _convertTimelineFields(Map<String, dynamic> serverData) {
    return {
      'id': serverData['id'],
      'date': serverData['date'],
      'duration': serverData['duration'],
      'appId': serverData['app_id'],
      'checkpointId': serverData['checkpoint_id'],
      'checkpoint_associations': serverData['checkpoint_associations'] ?? [],
    };
  }

  Map<String, dynamic> _convertTrackingStatusFields(Map<String, dynamic> serverData) {
    return {
      'isTracking': serverData['is_tracking'],
      'isPaused': serverData['is_paused'],
      'currentApp': serverData['current_app'],
      'currentSessionDuration': serverData['current_session_duration'],
      'sessionStartTime': serverData['session_start_time'],
      'currentCheckpointId': serverData['current_checkpoint_id'],
    };
  }

  Map<String, dynamic> _convertStatisticsFields(Map<String, dynamic> serverData) {
    final appData = serverData['app'] as Map<String, dynamic>;
    return {
      'app': _convertAppFields(appData),
      'totalDuration': serverData['total_duration'],
      'todayDuration': serverData['today_duration'],
      'weekDuration': serverData['week_duration'],
      'monthDuration': serverData['month_duration'],
      'averageSessionLength': serverData['average_session_length'],
      'recentSessions': (serverData['recent_sessions'] as List?)
          ?.map((session) => _convertTimelineFields(session as Map<String, dynamic>))
          .toList() ?? [],
    };
  }

  Map<String, dynamic> _convertCheckpointFields(Map<String, dynamic> serverData) {
    return {
      'id': serverData['id'],
      'name': serverData['name'],
      'description': serverData['description'],
      'created_at': serverData['created_at'],
      'valid_from': serverData['valid_from'],
      'is_active': serverData['is_active'],
      'color': serverData['color'],
      'app_id': serverData['app_id'],
      'duration': serverData['duration'],
      'sessions_count': serverData['sessions_count'],
      'last_updated': serverData['last_updated'],
      'activated_at': serverData['activated_at'],
    };
  }

  void _handleError(error) {
    if (_isDisposed) return;

    print('WebSocket error: $error');
    _isConnected = false;
    _isConnecting = false;

    // Emit connection state change
    if (!_connectionStateController.isClosed) {
      _connectionStateController.add(false);
    }

    _stopHeartbeat();
    _scheduleReconnect();
  }

  void _handleDisconnection() {
    if (_isDisposed) return;

    print('WebSocket disconnected');
    _isConnected = false;
    _isConnecting = false;

    // Emit connection state change
    if (!_connectionStateController.isClosed) {
      _connectionStateController.add(false);
    }

    _stopHeartbeat();
    _scheduleReconnect();
  }

  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(heartbeatInterval, (timer) {
      if (_isConnected && _channel != null && !_isDisposed) {
        try {
          _channel!.sink.add('ping');
          print('Sent ping to server');
        } catch (e) {
          print('Error sending ping: $e');
          _handleError(e);
        }
      }
    });
  }

  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  void _scheduleReconnect() {
    if (_isDisposed || _isConnecting) return;

    if (_reconnectAttempts >= maxReconnectAttempts) {
      print('Max reconnection attempts reached');
      _onConnectionFailed?.call();
      return;
    }

    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(reconnectDelay, () {
      if (_isDisposed) return;

      _reconnectAttempts++;
      print('Attempting to reconnect... (attempt $_reconnectAttempts)');
      connect();
    });
  }

  final List<Map<String, dynamic>> _messageQueue = [];

  void sendMessage(String type, Map<String, dynamic> payload) {
    final messageData = {'type': type, 'payload': payload};

    if (!_isConnected || _channel == null || _isDisposed) {
      // Queue message for when connection is established
      _messageQueue.add(messageData);
      print('WebSocket not connected, queuing message: $type');
      return;
    }

    _sendMessageNow(messageData);
  }

  void _sendMessageNow(Map<String, dynamic> messageData) {
    final message = json.encode({
      'type': messageData['type'],
      'payload': json.encode(messageData['payload']),
    });

    try {
      _channel!.sink.add(message);
      print('Sent WebSocket message: ${messageData['type']}');
    } catch (e) {
      print('Error sending WebSocket message: $e');
      _handleError(e);
    }
  }

  void _processMessageQueue() {
    if (_messageQueue.isEmpty) return;

    print('Processing ${_messageQueue.length} queued messages');
    final messages = List<Map<String, dynamic>>.from(_messageQueue);
    _messageQueue.clear();

    for (final messageData in messages) {
      _sendMessageNow(messageData);
    }
  }

  // API Methods
  Future<void> getAllApps() async {
    sendMessage('get_apps', {});
  }

  Future<void> getAppByName(String name) async {
    sendMessage('get_app_by_name', {'name': name});
  }

  Future<void> insertApp(String name) async {
    sendMessage('insert_app', {'name': name});
  }

  Future<void> deleteApp(int appId) async {
    sendMessage('delete_app', {'app_id': appId});
  }

  Future<void> getTrackingStatus() async {
    sendMessage('get_tracking_status', {});
  }

  Future<void> startTracking() async {
    sendMessage('tracking_command', {'action': 'start'});
  }

  Future<void> stopTracking() async {
    sendMessage('tracking_command', {'action': 'stop'});
  }

  Future<void> pauseTracking() async {
    sendMessage('tracking_command', {'action': 'pause'});
  }

  Future<void> resumeTracking() async {
    sendMessage('tracking_command', {'action': 'resume'});
  }

  Future<void> getTimeline({
    DateTime? startDate,
    DateTime? endDate,
    int? appId,
  }) async {
    final payload = <String, dynamic>{};
    if (startDate != null) payload['start_date'] = startDate.toIso8601String();
    if (endDate != null) payload['end_date'] = endDate.toIso8601String();
    if (appId != null) payload['app_id'] = appId;

    sendMessage('get_timeline', payload);
  }

  Future<void> getSessionCounts() async {
    sendMessage('get_session_counts', {});
  }

  Future<void> getStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final payload = <String, dynamic>{};
    if (startDate != null) payload['start_date'] = startDate.toIso8601String();
    if (endDate != null) payload['end_date'] = endDate.toIso8601String();

    sendMessage('get_statistics', payload);
  }

  // Checkpoint API Methods
  Future<void> getCheckpoints(int appId) async {
    sendMessage('get_checkpoints', {'app_id': appId});
  }

  Future<void> createCheckpoint(String name, String? description, DateTime validFrom, String color, int appId) async {
    final payload = {
      'name': name,
      'description': description,
      'valid_from': validFrom.toIso8601String(),
      'color': color,
      'app_id': appId,
    };
    sendMessage('create_checkpoint', payload);
  }

  Future<void> setActiveCheckpoint(int checkpointId, int appId) async {
    sendMessage('set_active_checkpoint', {'checkpoint_id': checkpointId, 'app_id': appId, 'is_active': true});
  }

  Future<void> clearActiveCheckpoint(int appId, int checkpointId) async {
    sendMessage('set_active_checkpoint', {'checkpoint_id': checkpointId, 'app_id': appId, 'is_active': false});
  }

  Future<void> deleteCheckpoint(int checkpointId, int appId) async {
    sendMessage('delete_checkpoint', {'checkpoint_id': checkpointId, 'app_id': appId});
  }

  Future<void> getCheckpointStats(int checkpointId) async {
    sendMessage('get_checkpoint_stats', {'checkpoint_id': checkpointId});
  }

  Future<void> disconnect() async {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
    _stopHeartbeat();

    await _cleanupConnection();
  }

  void dispose() {
    _isDisposed = true;
    disconnect();

    // Close all stream controllers
    if (!_trackingStatusController.isClosed) _trackingStatusController.close();
    if (!_appUpdateController.isClosed) _appUpdateController.close();
    if (!_sessionUpdateController.isClosed) _sessionUpdateController.close();
    if (!_appsListController.isClosed) _appsListController.close();
    if (!_timelineController.isClosed) _timelineController.close();
    if (!_statisticsController.isClosed) _statisticsController.close();
    if (!_sessionCountsController.isClosed) _sessionCountsController.close();
    if (!_connectionStateController.isClosed) _connectionStateController.close();
    if (!_checkpointsController.isClosed) _checkpointsController.close();
    if (!_checkpointsWithStatusController.isClosed) _checkpointsWithStatusController.close();
    if (!_activeCheckpointController.isClosed) _activeCheckpointController.close();
    if (!_checkpointStatsController.isClosed) _checkpointStatsController.close();
  }
}
