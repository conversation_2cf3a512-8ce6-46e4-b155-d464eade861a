use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON>ialEq, DeriveEntityModel, Deserialize, Serialize)]
#[sea_orm(table_name = "apps")]
pub struct Model {
    #[sea_orm(primary_key)]
    pub id: i32,
    pub duration: Option<i32>,
    pub launches: Option<i32>,
    pub longest_session: Option<i32>,
    pub name: Option<String>,
    pub product_name: Option<String>,
    pub longest_session_on: Option<chrono::NaiveDate>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::timeline::Entity")]
    Timeline,
    #[sea_orm(has_many = "super::checkpoints::Entity")]
    Checkpoints,
}

impl Related<super::timeline::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Timeline.def()
    }
}

impl Related<super::checkpoints::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Checkpoints.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
