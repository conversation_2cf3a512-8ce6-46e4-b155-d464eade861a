#!/usr/bin/env dart

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:args/args.dart';
import 'package:postgres/postgres.dart';
import 'package:shelf/shelf.dart';
import 'package:shelf/shelf_io.dart' as shelf_io;
import 'package:shelf_web_socket/shelf_web_socket.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

class Apps {
  final int id;
  final int? duration;
  final int? launches;
  final String? name;
  final int? longestSession;
  final String? productName;
  final DateTime? longestSessionOn;

  const Apps({
    required this.id,
    this.duration,
    this.launches,
    this.name,
    this.longestSession,
    this.productName,
    this.longestSessionOn,
  });

  factory Apps.fromRow(List<dynamic> row) => Apps(
    id: row[0] as int,
    duration: row[1] as int?,
    launches: row[2] as int?,
    longestSession: row[3] as int?,
    name: row[4] as String?,
    productName: row[5] as String?,
    longestSessionOn: row[6] != null ?
      (row[6] is DateTime ? row[6] as DateTime : DateTime.tryParse(row[6].toString()))
      : null,
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'duration': duration,
    'launches': launches,
    'name': name,
    'longest_session': longestSession,
    'product_name': productName,
    'longest_session_on': longestSessionOn?.toIso8601String(),
  };

  Apps copyWith({
    int? id,
    int? duration,
    int? launches,
    String? name,
    int? longestSession,
    String? productName,
    DateTime? longestSessionOn,
  }) => Apps(
    id: id ?? this.id,
    duration: duration ?? this.duration,
    launches: launches ?? this.launches,
    name: name ?? this.name,
    longestSession: longestSession ?? this.longestSession,
    productName: productName ?? this.productName,
    longestSessionOn: longestSessionOn ?? this.longestSessionOn,
  );
}

class Timeline {
  final int id;
  final String? date;
  final int? duration;
  final int appId;
  final int? checkpointId;

  const Timeline({
    required this.id,
    this.date,
    this.duration,
    required this.appId,
    this.checkpointId,
  });

  factory Timeline.fromRow(List<dynamic> row) => Timeline(
    id: row[0] as int,
    date: row[1] != null ?
      (row[1] is String ? row[1] as String : row[1].toString())
      : null,
    duration: row[2] as int?,
    appId: row[3] as int,
    checkpointId: row.length > 4 ? row[4] as int? : null,
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'date': date,
    'duration': duration,
    'app_id': appId,
    'checkpoint_id': checkpointId,
  };
}

class Checkpoint {
  final int id;
  final String name;
  final String? description;
  final DateTime createdAt;
  final DateTime validFrom;
  final String color;
  final int appId;

  const Checkpoint({
    required this.id,
    required this.name,
    this.description,
    required this.createdAt,
    required this.validFrom,
    required this.color,
    required this.appId,
  });

  factory Checkpoint.fromRow(List<dynamic> row) => Checkpoint(
    id: row[0] as int,
    name: row[1] as String,
    description: row[2] as String?,
    createdAt: row[3] as DateTime,
    validFrom: row[4] as DateTime,
    color: row[5] as String,
    appId: row[6] as int,
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'description': description,
    'created_at': createdAt.toIso8601String(),
    'valid_from': validFrom.toIso8601String(),
    'color': color,
    'app_id': appId,
  };

  Checkpoint copyWith({
    int? id,
    String? name,
    String? description,
    DateTime? createdAt,
    DateTime? validFrom,
    String? color,
    int? appId,
  }) => Checkpoint(
    id: id ?? this.id,
    name: name ?? this.name,
    description: description ?? this.description,
    createdAt: createdAt ?? this.createdAt,
    validFrom: validFrom ?? this.validFrom,
    color: color ?? this.color,
    appId: appId ?? this.appId,
  );
}

class CheckpointDuration {
  final int id;
  final int checkpointId;
  final int appId;
  final int duration;
  final int sessionsCount;
  final DateTime lastUpdated;

  const CheckpointDuration({
    required this.id,
    required this.checkpointId,
    required this.appId,
    required this.duration,
    required this.sessionsCount,
    required this.lastUpdated,
  });

  factory CheckpointDuration.fromRow(List<dynamic> row) => CheckpointDuration(
    id: row[0] as int,
    checkpointId: row[1] as int,
    appId: row[2] as int,
    duration: row[3] as int,
    sessionsCount: row[4] as int,
    lastUpdated: row[5] as DateTime,
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'checkpoint_id': checkpointId,
    'app_id': appId,
    'duration': duration,
    'sessions_count': sessionsCount,
    'last_updated': lastUpdated.toIso8601String(),
  };
}

class TrackingStatus {
  final bool isTracking;
  final bool isPaused;
  final String? currentApp;
  final int currentSessionDuration;
  final String? sessionStartTime;
  final List<int> activeCheckpointIds;

  const TrackingStatus({
    required this.isTracking,
    required this.isPaused,
    this.currentApp,
    required this.currentSessionDuration,
    this.sessionStartTime,
    this.activeCheckpointIds = const [],
  });

  Map<String, dynamic> toJson() => {
    'is_tracking': isTracking,
    'is_paused': isPaused,
    'current_app': currentApp,
    'current_session_duration': currentSessionDuration,
    'session_start_time': sessionStartTime,
    'active_checkpoint_ids': activeCheckpointIds,
  };

  TrackingStatus copyWith({
    bool? isTracking,
    bool? isPaused,
    String? currentApp,
    int? currentSessionDuration,
    String? sessionStartTime,
    List<int>? activeCheckpointIds,
  }) => TrackingStatus(
    isTracking: isTracking ?? this.isTracking,
    isPaused: isPaused ?? this.isPaused,
    currentApp: currentApp ?? this.currentApp,
    currentSessionDuration: currentSessionDuration ?? this.currentSessionDuration,
    sessionStartTime: sessionStartTime ?? this.sessionStartTime,
    activeCheckpointIds: activeCheckpointIds ?? this.activeCheckpointIds,
  );
}

class ProcessUpdate {
  final String processName;
  final bool isRunning;
  final int currentDuration;
  final int totalDuration;

  const ProcessUpdate({
    required this.processName,
    required this.isRunning,
    required this.currentDuration,
    required this.totalDuration,
  });

  Map<String, dynamic> toJson() => {
    'process_name': processName,
    'is_running': isRunning,
    'current_duration': currentDuration,
    'total_duration': totalDuration,
  };
}

class AppStatistics {
  final Apps app;
  final int totalDuration;
  final int todayDuration;
  final int weekDuration;
  final int monthDuration;
  final double averageSessionLength;
  final List<Timeline> recentSessions;

  const AppStatistics({
    required this.app,
    required this.totalDuration,
    required this.todayDuration,
    required this.weekDuration,
    required this.monthDuration,
    required this.averageSessionLength,
    required this.recentSessions,
  });

  Map<String, dynamic> toJson() => {
    'app': app.toJson(),
    'total_duration': totalDuration,
    'today_duration': todayDuration,
    'week_duration': weekDuration,
    'month_duration': monthDuration,
    'average_session_length': averageSessionLength,
    'recent_sessions': recentSessions.map((s) => s.toJson()).toList(),
  };
}

class SessionCount {
  final int appId;
  final int sessionCount;

  const SessionCount({required this.appId, required this.sessionCount});

  Map<String, dynamic> toJson() => {
    'app_id': appId,
    'session_count': sessionCount,
  };
}

class WebSocketMessage {
  final String type;
  final String payload;

  const WebSocketMessage({required this.type, required this.payload});

  factory WebSocketMessage.fromJson(Map<String, dynamic> json) => WebSocketMessage(
    type: json['type'] as String,
    payload: json['payload'] as String,
  );

  Map<String, dynamic> toJson() => {'type': type, 'payload': payload};
}

class WebSocketCommand {
  final String type;
  final String payload;

  const WebSocketCommand({required this.type, required this.payload});

  factory WebSocketCommand.fromJson(Map<String, dynamic> json) => WebSocketCommand(
    type: json['type'] as String,
    payload: json['payload'] as String,
  );
}

class WebSocketClient {
  final WebSocketChannel channel;
  final String id;
  final DateTime connectedAt;
  final Set<String> subscriptions = {};

  WebSocketClient({
    required this.channel,
    required this.id,
    required this.connectedAt,
  });

  void subscribe(String eventType) => subscriptions.add(eventType);
  void unsubscribe(String eventType) => subscriptions.remove(eventType);
  bool isSubscribed(String eventType) => subscriptions.contains(eventType);

  bool send(String message) {
    try {
      channel.sink.add(message);
      return true;
    } catch (e) {
      return false;
    }
  }
}

class DatabaseOperation {
  final String query;
  final Map<String, dynamic>? parameters;
  final String? errorMessage;
  final Completer<void>? completer;

  const DatabaseOperation({
    required this.query,
    this.parameters,
    this.errorMessage,
    this.completer,
  });
}

class EventTypes {
  static const String trackingStatusUpdate = 'tracking_status_update';
  static const String appUpdate = 'app_update';
  static const String appsList = 'apps_list';
  static const String timelineData = 'timeline_data';
  static const String statisticsData = 'statistics_data';
  static const String sessionCounts = 'session_counts';
  static const String error = 'error';
  static const String clientId = 'client_id';
  static const String subscribed = 'subscribed';
  static const String unsubscribed = 'unsubscribed';
  static const String app = 'app';
  static const String checkpointUpdate = 'checkpoint_update';
  static const String checkpointsList = 'checkpoints_list';
  static const String checkpointStats = 'checkpoint_stats';
  static const String checkpointCreated = 'checkpoint_created';
}

class Commands {
  static const String subscribe = 'subscribe';
  static const String unsubscribe = 'unsubscribe';
  static const String getApps = 'get_apps';
  static const String getAppByName = 'get_app_by_name';
  static const String getTrackingStatus = 'get_tracking_status';
  static const String getTimeline = 'get_timeline';
  static const String getSessionCounts = 'get_session_counts';
  static const String getStatistics = 'get_statistics';
  static const String getCheckpoints = 'get_checkpoints';
  static const String createCheckpoint = 'create_checkpoint';
  static const String setActiveCheckpoint = 'set_active_checkpoint';
  static const String getCheckpointStats = 'get_checkpoint_stats';
  static const String deleteCheckpoint = 'delete_checkpoint';
}

class TimeTracker {
  // Database & Core State
  late final PostgreSQLConnection _db;
  final Map<String, bool> _processes = {};
  final Map<String, Apps> _appsCache = {};
  final Map<String, WebSocketClient> _clients = {};
  final Map<int, Checkpoint> _checkpointsCache = {};

  // Reactive Streams
  late final StreamController<TrackingStatus> _trackingStatusController;
  late final StreamController<Apps> _appUpdateController;
  late final StreamController<DatabaseOperation> _dbOperationController;
  late final StreamController<WebSocketMessage> _broadcastController;

  // State
  TrackingStatus _trackingStatus = const TrackingStatus(
    isTracking: false,
    isPaused: false,
    currentSessionDuration: 0,
  );
  List<int> _activeCheckpointIds = [];

  bool _pause = false;
  Timer? _cacheRefreshTimer;
  Timer? _dbBatchTimer;
  final List<DatabaseOperation> _pendingDbOps = [];

  TimeTracker() {
    _initializeStreams();
    _setupStreamSubscriptions();
    _startPeriodicTasks();
  }

  void _initializeStreams() {
    _trackingStatusController = StreamController<TrackingStatus>.broadcast();
    _appUpdateController = StreamController<Apps>.broadcast();
    _dbOperationController = StreamController<DatabaseOperation>.broadcast();
    _broadcastController = StreamController<WebSocketMessage>.broadcast();
  }

  void _setupStreamSubscriptions() {
    _trackingStatusController.stream.listen((status) {
      _trackingStatus = status;
      _broadcastController.add(_createMessage(
        EventTypes.trackingStatusUpdate,
        '{"status": ${jsonEncode(status.toJson())}}'
      ));
    });

    _appUpdateController.stream
        .distinct((prev, next) => prev.id == next.id && prev.duration == next.duration)
        .listen((app) {
      _appsCache[app.name ?? ''] = app;
      _broadcastController.add(_createMessage(
        EventTypes.appUpdate,
        '{"app": ${jsonEncode(app.toJson())}}'
      ));
    });

    _dbOperationController.stream.listen((operation) {
      _pendingDbOps.add(operation);
    });

    _broadcastController.stream.listen(_broadcastToSubscribedClients);
  }

  void _startPeriodicTasks() {
    _cacheRefreshTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _refreshAppsCache();
    });

    _dbBatchTimer = Timer.periodic(const Duration(seconds: 5), (_) {
      _processPendingDbOperations();
    });
  }

  Future<void> _initDatabase() async {
    _db = PostgreSQLConnection(
      '10.0.0.5',
      5432,
      'time_tracker',
      username: 'postgres',
      password: 'root',
    );
    await _db.open();
    await _refreshAppsCache();
    await _refreshCheckpointsCache();
  }

  Future<void> _refreshAppsCache() async {
    try {
      final apps = await _getAllApps();
      _appsCache.clear();
      for (final app in apps) {
        if (app.name != null) {
          _appsCache[app.name!] = app;
        }
      }
    } catch (e) {
      print('Failed to refresh apps cache: $e');
    }
  }

  Future<void> _processPendingDbOperations() async {
    if (_pendingDbOps.isEmpty) return;

    final operations = List<DatabaseOperation>.from(_pendingDbOps);
    _pendingDbOps.clear();

    for (final op in operations) {
      try {
        if (op.parameters != null) {
          await _db.query(op.query, substitutionValues: op.parameters!);
        } else {
          await _db.query(op.query);
        }
        op.completer?.complete();
      } catch (e) {
        print('Database operation failed: $e');
        if (op.errorMessage != null) print(op.errorMessage!);
        op.completer?.completeError(e);
      }
    }
  }

  Future<List<Apps>> _getAllApps() async {
    final result = await _db.query('SELECT * FROM apps ORDER BY id');
    return result.map(Apps.fromRow).toList();
  }

  Future<Apps?> _getAppByName(String name) async {
    if (_appsCache.containsKey(name)) {
      return _appsCache[name];
    }

    final result = await _db.query(
      'SELECT * FROM apps WHERE name = @name',
      substitutionValues: {'name': name},
    );

    if (result.isEmpty) return null;

    final app = Apps.fromRow(result.first);
    _appsCache[name] = app;
    return app;
  }

  Future<void> _insertNewApp(String name) async {
    final apps = await _getAllApps();
    final newId = apps.isEmpty ? 1 : apps.last.id + 1;

    final completer = Completer<void>();
    _dbOperationController.add(DatabaseOperation(
      query: 'INSERT INTO apps (id, name) VALUES (@id, @name)',
      parameters: {'id': newId, 'name': name},
      completer: completer,
    ));

    await completer.future;
    await _refreshAppsCache();
  }

  Future<void> _deleteApp(int id) async {
    final completer = Completer<void>();
    _dbOperationController.add(DatabaseOperation(
      query: 'DELETE FROM apps WHERE id = @id',
      parameters: {'id': id},
      completer: completer,
    ));

    await completer.future;
    await _refreshAppsCache();
  }

  Future<List<Timeline>> _getAllTimeline() async {
    final result = await _db.query('SELECT * FROM timeline ORDER BY date DESC');
    return result.map(Timeline.fromRow).toList();
  }

  Future<List<Timeline>> _getTimelineByApp(int appId) async {
    final result = await _db.query(
      'SELECT * FROM timeline WHERE app_id = @app_id ORDER BY date DESC',
      substitutionValues: {'app_id': appId},
    );
    return result.map(Timeline.fromRow).toList();
  }

  Future<Map<int, List<int>>> _getTimelineCheckpointAssociations() async {
    final result = await _db.query('''
      SELECT timeline_id, checkpoint_id
      FROM timeline_checkpoints
      ORDER BY timeline_id, checkpoint_id
    ''');

    final associations = <int, List<int>>{};
    for (final row in result) {
      final timelineId = row[0] as int;
      final checkpointId = row[1] as int;

      if (!associations.containsKey(timelineId)) {
        associations[timelineId] = [];
      }
      associations[timelineId]!.add(checkpointId);
    }

    return associations;
  }

  Future<List<SessionCount>> _getSessionCountsByApp() async {
    final apps = _appsCache.values.toList();
    if (apps.isEmpty) {
      final dbApps = await _getAllApps();
      return await _calculateSessionCounts(dbApps);
    }
    return await _calculateSessionCounts(apps);
  }

  Future<List<SessionCount>> _calculateSessionCounts(List<Apps> apps) async {
    final futures = apps.map((app) async {
      final timeline = await _getTimelineByApp(app.id);
      return SessionCount(appId: app.id, sessionCount: timeline.length);
    });

    return await Future.wait(futures);
  }

  Future<List<AppStatistics>> _calculateStatistics() async {
    final apps = _appsCache.values.toList();
    final today = _getToday();

    final futures = apps.map((app) async {
      final timeline = await _getTimelineByApp(app.id);

      var todayDuration = 0;
      var weekDuration = 0;
      var monthDuration = 0;

      for (final tl in timeline) {
        final duration = tl.duration ?? 0;
        if (tl.date == today) todayDuration += duration;
        weekDuration += duration;
        monthDuration += duration;
      }

      // Safely calculate average session length
      double avgSession = 0.0;
      if (timeline.isNotEmpty) {
        final totalDuration = timeline.map((t) => t.duration ?? 0).reduce((a, b) => a + b);
        if (totalDuration > 0) {
          final calculated = totalDuration / timeline.length;
          // Ensure the result is finite and not NaN
          if (calculated.isFinite && !calculated.isNaN) {
            avgSession = calculated;
          }
        }
      }

      return AppStatistics(
        app: app,
        totalDuration: app.duration ?? 0,
        todayDuration: todayDuration,
        weekDuration: weekDuration,
        monthDuration: monthDuration,
        averageSessionLength: avgSession,
        recentSessions: timeline.take(10).toList(),
      );
    });

    return await Future.wait(futures);
  }

  WebSocketMessage _createMessage(String type, String payload) =>
      WebSocketMessage(type: type, payload: payload);

  WebSocketMessage _createErrorMessage(String errorMsg) =>
      _createMessage(EventTypes.error, '{"message": "$errorMsg"}');

  void _broadcastToSubscribedClients(WebSocketMessage message) {
    final messageJson = jsonEncode(message.toJson());
    final disconnectedClients = <String>[];

    for (final client in _clients.values) {
      if (client.isSubscribed(message.type) || client.subscriptions.isEmpty) {
        if (!client.send(messageJson)) {
          disconnectedClients.add(client.id);
        }
      }
    }

    for (final clientId in disconnectedClients) {
      _clients.remove(clientId);
    }
  }

  Future<WebSocketMessage> _handleWebSocketCommand(WebSocketCommand command) async {
    try {
      switch (command.type) {
        case Commands.subscribe:
          return _handleSubscription(command.payload, true);
        case Commands.unsubscribe:
          return _handleSubscription(command.payload, false);
        case Commands.getApps:
          return _handleGetApps();
        case Commands.getAppByName:
          return _handleGetAppByName(command.payload);
        case Commands.getTrackingStatus:
          return _handleGetTrackingStatus();
        case Commands.getTimeline:
          return _handleGetTimeline();
        case Commands.getSessionCounts:
          return _handleGetSessionCounts();
        case Commands.getStatistics:
          return _handleGetStatistics();
        case Commands.getCheckpoints:
          return _handleGetCheckpoints(command.payload);
        case Commands.createCheckpoint:
          return _handleCreateCheckpoint(command.payload);
        case Commands.setActiveCheckpoint:
          return _handleSetActiveCheckpoint(command.payload);
        case Commands.getCheckpointStats:
          return _handleGetCheckpointStats(command.payload);
        case Commands.deleteCheckpoint:
          return _handleDeleteCheckpoint(command.payload);
        default:
          return _createErrorMessage('Unknown command type: ${command.type}');
      }
    } catch (e) {
      return _createErrorMessage('Command error: $e');
    }
  }

  WebSocketMessage _handleSubscription(String payload, bool subscribe) {
    final data = jsonDecode(payload) as Map<String, dynamic>;
    final clientId = data['client_id'] as String?;
    final eventType = data['event_type'] as String;

    if (clientId != null && _clients.containsKey(clientId)) {
      if (subscribe) {
        _clients[clientId]!.subscribe(eventType);
        return _createMessage(EventTypes.subscribed, '{"event_type": "$eventType"}');
      } else {
        _clients[clientId]!.unsubscribe(eventType);
        return _createMessage(EventTypes.unsubscribed, '{"event_type": "$eventType"}');
      }
    }
    return _createErrorMessage('Client not found');
  }

  WebSocketMessage _handleGetApps() {
    final apps = _appsCache.values.toList();
    final appsJson = jsonEncode(apps.map((a) => a.toJson()).toList());
    return _createMessage(EventTypes.appsList, '{"apps": $appsJson}');
  }

  Future<WebSocketMessage> _handleGetAppByName(String payload) async {
    final nameData = jsonDecode(payload) as Map<String, dynamic>;
    final name = nameData['name'] as String;
    final app = await _getAppByName(name);

    if (app != null) {
      final appJson = jsonEncode(app.toJson());
      return _createMessage(EventTypes.app, '{"app": $appJson}');
    }
    return _createErrorMessage('App not found');
  }

  WebSocketMessage _handleGetTrackingStatus() {
    final statusJson = jsonEncode(_trackingStatus.toJson());
    return _createMessage(EventTypes.trackingStatusUpdate, '{"status": $statusJson}');
  }

  Future<WebSocketMessage> _handleGetTimeline() async {
    final timeline = await _getAllTimeline();
    final checkpointAssociations = await _getTimelineCheckpointAssociations();

    // Enhance timeline data with checkpoint associations
    final enhancedTimeline = timeline.map((t) {
      final associations = checkpointAssociations[t.id] ?? [];
      final timelineJson = t.toJson();
      timelineJson['checkpoint_associations'] = associations;
      return timelineJson;
    }).toList();

    final timelineJson = jsonEncode(enhancedTimeline);
    return _createMessage(EventTypes.timelineData, '{"timeline": $timelineJson}');
  }

  Future<WebSocketMessage> _handleGetSessionCounts() async {
    final counts = await _getSessionCountsByApp();
    final countsJson = jsonEncode(counts.map((c) => c.toJson()).toList());
    return _createMessage(EventTypes.sessionCounts, '{"counts": $countsJson}');
  }

  Future<WebSocketMessage> _handleGetStatistics() async {
    final statistics = await _calculateStatistics();
    final statisticsJson = jsonEncode(statistics.map((s) => s.toJson()).toList());
    return _createMessage(EventTypes.statisticsData, '{"statistics": $statisticsJson}');
  }

  Future<WebSocketMessage> _handleGetCheckpoints(String payload) async {
    try {
      final data = jsonDecode(payload) as Map<String, dynamic>;
      final appId = data['app_id'] as int?;

      if (appId == null) {
        return _createErrorMessage('app_id is required for getting checkpoints');
      }

      final checkpoints = await _getCheckpointsForApp(appId);
      final activeCheckpoints = await _getActiveCheckpointsForApp(appId);
      final activeCheckpointIds = activeCheckpoints.map((c) => c.id).toSet();

      final checkpointsWithStatus = checkpoints.map((c) => {
        ...c.toJson(),
        'is_active': activeCheckpointIds.contains(c.id),
      }).toList();
      final checkpointsJson = jsonEncode(checkpointsWithStatus);
      return _createMessage(EventTypes.checkpointsList, '{"checkpoints": $checkpointsJson}');
    } catch (e) {
      return _createErrorMessage('Failed to get checkpoints: $e');
    }
  }

  Future<WebSocketMessage> _handleCreateCheckpoint(String payload) async {
    try {
      final data = jsonDecode(payload) as Map<String, dynamic>;
      final name = data['name'] as String;
      final description = data['description'] as String?;
      final validFromStr = data['valid_from'] as String?;
      final color = data['color'] as String? ?? '#2196F3';
      final appId = data['app_id'] as int?;

      if (appId == null) {
        return _createErrorMessage('app_id is required for creating checkpoints');
      }

      final validFrom = validFromStr != null
          ? DateTime.parse(validFromStr)
          : DateTime.now();

      await _createCheckpoint(name, description, validFrom, color, appId);

      final checkpoints = await _getCheckpointsForApp(appId);
      final activeCheckpoints = await _getActiveCheckpointsForApp(appId);
      final activeCheckpointIds = activeCheckpoints.map((c) => c.id).toSet();

      final checkpointsWithStatus = checkpoints.map((c) => {
        ...c.toJson(),
        'is_active': activeCheckpointIds.contains(c.id),
      }).toList();
      final checkpointsJson = jsonEncode(checkpointsWithStatus);
      return _createMessage(EventTypes.checkpointCreated, '{"checkpoints": $checkpointsJson}');
    } catch (e) {
      return _createErrorMessage('Failed to create checkpoint: $e');
    }
  }

  Future<WebSocketMessage> _handleSetActiveCheckpoint(String payload) async {
    try {
      final data = jsonDecode(payload) as Map<String, dynamic>;
      final checkpointId = data['checkpoint_id'] as int;
      final appId = data['app_id'] as int?;

      if (appId == null) {
        return _createErrorMessage('app_id is required for setting active checkpoints');
      }

      if (checkpointId == -1) {
        // Special case: -1 means clear all active checkpoints for this app
        await _clearAllCheckpointsForApp(appId);
      } else {
        // Normal case: toggle this specific checkpoint for this app
        final activeCheckpoints = await _getActiveCheckpointsForApp(appId);
        final isActive = activeCheckpoints.any((c) => c.id == checkpointId);

        if (isActive) {
          // Deactivate this checkpoint for this app
          await _deactivateCheckpoint(checkpointId, appId);
        } else {
          // Activate this checkpoint for this app
          await _activateCheckpoint(checkpointId, appId);
        }
      }

      // Send updated checkpoints list with active status for this app
      final checkpoints = await _getCheckpointsForApp(appId);
      final activeCheckpoints = await _getActiveCheckpointsForApp(appId);
      final activeCheckpointIds = activeCheckpoints.map((c) => c.id).toSet();

      final checkpointsWithStatus = checkpoints.map((c) => {
        ...c.toJson(),
        'is_active': activeCheckpointIds.contains(c.id),
      }).toList();
      final checkpointsJson = jsonEncode(checkpointsWithStatus);
      return _createMessage(EventTypes.checkpointsList, '{"checkpoints": $checkpointsJson}');
    } catch (e) {
      return _createErrorMessage('Failed to set active checkpoint: $e');
    }
  }

  Future<WebSocketMessage> _handleGetCheckpointStats(String payload) async {
    try {
      final data = jsonDecode(payload) as Map<String, dynamic>;
      final checkpointId = data['checkpoint_id'] as int;

      final durations = await _getCheckpointDurations([checkpointId]);
      final durationsJson = jsonEncode(durations.map((d) => d.toJson()).toList());

      return _createMessage(EventTypes.checkpointStats, '{"checkpoint_id": $checkpointId, "durations": $durationsJson}');
    } catch (e) {
      return _createErrorMessage('Failed to get checkpoint stats: $e');
    }
  }

  Future<WebSocketMessage> _handleDeleteCheckpoint(String payload) async {
    try {
      final data = jsonDecode(payload) as Map<String, dynamic>;
      final checkpointId = data['checkpoint_id'] as int;
      final appId = data['app_id'] as int?;

      if (appId == null) {
        return _createErrorMessage('app_id is required for deleting checkpoints');
      }

      await _deleteCheckpoints([checkpointId]);

      // Send updated checkpoints list for this app
      final checkpoints = await _getCheckpointsForApp(appId);
      final activeCheckpoints = await _getActiveCheckpointsForApp(appId);
      final activeCheckpointIds = activeCheckpoints.map((c) => c.id).toSet();

      final checkpointsWithStatus = checkpoints.map((c) => {
        ...c.toJson(),
        'is_active': activeCheckpointIds.contains(c.id),
      }).toList();
      final checkpointsJson = jsonEncode(checkpointsWithStatus);
      return _createMessage(EventTypes.checkpointsList, '{"checkpoints": $checkpointsJson}');
    } catch (e) {
      return _createErrorMessage('Failed to delete checkpoint: $e');
    }
  }

  Future<void> _startWebSocketServer() async {
    final handler = webSocketHandler((WebSocketChannel webSocket) {
      final clientId = DateTime.now().millisecondsSinceEpoch.toString();
      final client = WebSocketClient(
        channel: webSocket,
        id: clientId,
        connectedAt: DateTime.now(),
      );

      _clients[clientId] = client;
      print('WebSocket client connected: $clientId');

      client.send(jsonEncode({
        'type': EventTypes.clientId,
        'payload': '{"client_id": "$clientId"}'
      }));

      webSocket.stream.listen(
        (message) async {
          final payload = message.toString();

          if (payload == 'ping') {
            client.send('pong');
            return;
          }

          if (payload.trim().isEmpty) return;

          try {
            final command = WebSocketCommand.fromJson(
              jsonDecode(payload) as Map<String, dynamic>
            );
            final response = await _handleWebSocketCommand(command);
            client.send(jsonEncode(response.toJson()));
          } catch (e) {
            final errorMsg = _createErrorMessage('Invalid JSON format: $e');
            client.send(jsonEncode(errorMsg.toJson()));
          }
        },
        onDone: () {
          _clients.remove(clientId);
          print('WebSocket client disconnected: $clientId');
        },
        onError: (error) {
          print('WebSocket error for client $clientId: $error');
          _clients.remove(clientId);
        },
      );
    });

    await shelf_io.serve(handler, '0.0.0.0', 6754);
    print('WebSocket server running on ws://localhost:6754');
  }

  Future<void> _monitorProcess(String process, Apps app) async {
    print('"$process" is running.');

    final initLaunches = app.launches;
    var duration = app.duration ?? 0;
    final longestSession = app.longestSession;
    var currentDuration = 0;
    var launched = false;

    // Get current active checkpoints for this specific app
    final activeCheckpoints = await _getActiveCheckpointsForApp(app.id);
    final activeCheckpointIds = activeCheckpoints.map((c) => c.id).toList();

    _trackingStatusController.add(_trackingStatus.copyWith(
      currentApp: process,
      isTracking: true,
      activeCheckpointIds: activeCheckpointIds,
    ));

    while (await _isProcessRunning(process)) {
      await Future.delayed(const Duration(seconds: 1));

      if (!launched) {
        final launches = (initLaunches ?? 0) + 1;
        _dbOperationController.add(DatabaseOperation(
          query: 'UPDATE apps SET launches = @launches WHERE name = @name',
          parameters: {'launches': launches, 'name': process},
        ));

        // Update sessions_count for all active checkpoints for this app
        for (final checkpointId in activeCheckpointIds) {
          _dbOperationController.add(DatabaseOperation(
            query: '''
              INSERT INTO checkpoint_durations (checkpoint_id, app_id, duration, sessions_count, last_updated)
              VALUES (@checkpoint_id, @app_id, 0, 1, CURRENT_TIMESTAMP)
              ON CONFLICT (checkpoint_id, app_id)
              DO UPDATE SET
                sessions_count = checkpoint_durations.sessions_count + 1,
                last_updated = CURRENT_TIMESTAMP
            ''',
            parameters: {
              'checkpoint_id': checkpointId,
              'app_id': app.id,
            },
            errorMessage: 'Could not update checkpoint sessions for app: $process.',
          ));
        }

        launched = true;
      }

      duration++;
      currentDuration++;

      _dbOperationController.add(DatabaseOperation(
        query: 'UPDATE apps SET duration = @duration WHERE name = @name',
        parameters: {'duration': duration, 'name': process},
      ));

      final nowStr = _getToday();
      // Use the new multiple checkpoints function
      _dbOperationController.add(DatabaseOperation(
        query: "SELECT upsert_timeline_multiple_checkpoints(@app_name, @date)",
        parameters: {
          'app_name': process,
          'date': nowStr,
        },
        errorMessage: 'Could not update Timeline for app: $process.',
      ));

      _trackingStatusController.add(_trackingStatus.copyWith(
        currentSessionDuration: currentDuration,
        activeCheckpointIds: activeCheckpointIds,
      ));

      final updatedApp = app.copyWith(duration: duration);
      _appUpdateController.add(updatedApp);
    }

    // Reset process state to allow tracking the same process again
    _processes[process] = false;

    print('Session detail for app: "$process"');
    print('Total Duration: $duration.');
    print('Current session duration: $currentDuration.');

    await _sendNotification(
      'time_tracker',
      'process: "$process" | current: $currentDuration | total: $duration',
    );

    if (currentDuration > (longestSession ?? 0)) {
      _dbOperationController.add(DatabaseOperation(
        query: 'UPDATE apps SET longest_session = @duration WHERE name = @name',
        parameters: {'duration': currentDuration, 'name': process},
      ));

      final nowStr = _getToday();
      _dbOperationController.add(DatabaseOperation(
        query: "UPDATE apps SET longest_session_on = '$nowStr' WHERE name = '$process'",
        errorMessage: 'Could not update app: $process.',
      ));

      print('New longest session for "$process" on: "$nowStr" with duration: $currentDuration.');
    }

    _trackingStatusController.add(_trackingStatus.copyWith(
      isTracking: false,
      currentApp: null,
      currentSessionDuration: 0,
      activeCheckpointIds: [],
    ));
  }

  Future<bool> _isProcessRunning(String process) async {
    final result = await Process.run('pgrep', ['-f', process]);
    return result.exitCode == 0;
  }

  Future<void> _sendNotification(String title, String message) async {
    try {
      await Process.run('notify-send', [title, message]);
    } catch (e) {
      print('Failed to send notification: $e');
    }
  }

  String _getToday() => DateTime.now().toString().split(' ')[0];

  Future<void> startDiscovery({bool debug = false}) async {
    if (debug) print('Starting discovery.');

    await _startWebSocketServer();

    while (true) {
      if (_pause) {
        await Future.delayed(const Duration(seconds: 1));
        continue;
      }

      for (final process in _processes.keys) {
        if (await _isProcessRunning(process) && !_processes[process]!) {
          if (debug) print('Found running process "$process".');

          await _sendNotification('time_tracker', 'Started tracking "$process"');

          final app = await _getAppByName(process);
          if (app == null) {
            print('Could not find App: "$process".');
            continue;
          }

          _processes[process] = true;
          unawaited(_monitorProcess(process, app));
        }
      }

      await Future.delayed(const Duration(seconds: 1));
    }
  }

  Future<void> _initializeProcesses() async {
    final apps = await _getAllApps();
    for (final app in apps) {
      if (app.name != null) {
        _processes[app.name!] = false;
      }
    }
  }

  Future<void> dispose() async {
    _cacheRefreshTimer?.cancel();
    _dbBatchTimer?.cancel();

    await _trackingStatusController.close();
    await _appUpdateController.close();
    await _dbOperationController.close();
    await _broadcastController.close();

    for (final client in _clients.values) {
      await client.channel.sink.close();
    }
    _clients.clear();

    await _db.close();
  }

  // Checkpoint database methods
  Future<List<Checkpoint>> _getAllCheckpoints() async {
    final result = await _db.query('''
      SELECT id, name, description, created_at, valid_from, color, app_id
      FROM checkpoints
      ORDER BY created_at DESC
    ''');
    return result.map(Checkpoint.fromRow).toList();
  }

  Future<List<Checkpoint>> _getCheckpointsForApp(int appId) async {
    final result = await _db.query('''
      SELECT id, name, description, created_at, valid_from, color, app_id
      FROM checkpoints
      WHERE app_id = @app_id
      ORDER BY created_at DESC
    ''', substitutionValues: {'app_id': appId});
    return result.map(Checkpoint.fromRow).toList();
  }

  Future<List<Checkpoint>> _getActiveCheckpoints() async {
    final result = await _db.query('''
      SELECT c.id, c.name, c.description, c.created_at, c.valid_from, c.color, c.app_id
      FROM checkpoints c
      JOIN active_checkpoints ac ON c.id = ac.checkpoint_id
      ORDER BY c.valid_from DESC
    ''');
    return result.map(Checkpoint.fromRow).toList();
  }

  Future<List<Checkpoint>> _getActiveCheckpointsForApp(int appId) async {
    final result = await _db.query('''
      SELECT c.id, c.name, c.description, c.created_at, c.valid_from, c.color, c.app_id
      FROM checkpoints c
      JOIN active_checkpoints ac ON c.id = ac.checkpoint_id
      WHERE ac.app_id = @app_id
      ORDER BY c.valid_from DESC
    ''', substitutionValues: {'app_id': appId});
    return result.map(Checkpoint.fromRow).toList();
  }

  Future<void> _createCheckpoint(String name, String? description, DateTime validFrom, String color, int appId) async {
    final completer = Completer<void>();
    _dbOperationController.add(DatabaseOperation(
      query: '''
        INSERT INTO checkpoints (name, description, valid_from, color, app_id)
        VALUES (@name, @description, @valid_from, @color, @app_id)
      ''',
      parameters: {
        'name': name,
        'description': description,
        'valid_from': validFrom,
        'color': color,
        'app_id': appId,
      },
      completer: completer,
    ));
    await completer.future;
    await _refreshCheckpointsCache();
  }

  Future<void> _setActiveCheckpoints(List<int> checkpointIds) async {
    // Clear all active checkpoints first
    final completer1 = Completer<void>();
    _dbOperationController.add(DatabaseOperation(
      query: 'SELECT clear_all_active_checkpoints()',
      completer: completer1,
    ));
    await completer1.future;

    // Activate the selected checkpoints
    for (final checkpointId in checkpointIds) {
      final completer = Completer<void>();
      _dbOperationController.add(DatabaseOperation(
        query: 'SELECT activate_checkpoint(@checkpoint_id, @app_id)',
        parameters: {'checkpoint_id': checkpointId, 'app_id': 1}, // Default app_id for backward compatibility
        completer: completer,
      ));
      await completer.future;
    }

    _activeCheckpointIds = checkpointIds;
    await _refreshCheckpointsCache();

    // Update tracking status
    _trackingStatusController.add(_trackingStatus.copyWith(
      activeCheckpointIds: _activeCheckpointIds,
    ));
  }

  Future<void> _deleteCheckpoints(List<int> checkpointIds) async {
    for (final checkpointId in checkpointIds) {
      final completer = Completer<void>();
      _dbOperationController.add(DatabaseOperation(
        query: 'DELETE FROM checkpoints WHERE id = @id',
        parameters: {'id': checkpointId},
        completer: completer,
      ));
      await completer.future;
    }
    await _refreshCheckpointsCache();
  }

  Future<void> _refreshCheckpointsCache() async {
    try {
      final checkpoints = await _getAllCheckpoints();
      _checkpointsCache.clear();
      for (final checkpoint in checkpoints) {
        _checkpointsCache[checkpoint.id] = checkpoint;
      }

      // Update current checkpoints
      final activeCheckpoints = await _getActiveCheckpoints();
      _activeCheckpointIds = activeCheckpoints.map((c) => c.id).toList();
    } catch (e) {
      print('Failed to refresh checkpoints cache: $e');
    }
  }

  Future<List<CheckpointDuration>> _getCheckpointDurations(List<int> checkpointIds) async {
    if (checkpointIds.isEmpty) return [];

    final placeholders = List.generate(checkpointIds.length, (index) => '@checkpoint_id_$index').join(', ');
    final parameters = <String, dynamic>{};
    for (int i = 0; i < checkpointIds.length; i++) {
      parameters['checkpoint_id_$i'] = checkpointIds[i];
    }

    final result = await _db.query('''
      SELECT id, checkpoint_id, app_id, duration, sessions_count, last_updated
      FROM checkpoint_durations
      WHERE checkpoint_id IN ($placeholders)
      ORDER BY duration DESC
    ''', substitutionValues: parameters);
    return result.map(CheckpointDuration.fromRow).toList();
  }

  Future<void> _clearAllCheckpoints() async {
    final completer = Completer<void>();
    _dbOperationController.add(DatabaseOperation(
      query: 'SELECT clear_all_active_checkpoints()',
      completer: completer,
    ));
    await completer.future;
    _activeCheckpointIds = [];
    await _refreshCheckpointsCache();
    _trackingStatusController.add(_trackingStatus.copyWith(
      activeCheckpointIds: [],
    ));
  }

  Future<void> _clearAllCheckpointsForApp(int appId) async {
    final completer = Completer<void>();
    _dbOperationController.add(DatabaseOperation(
      query: 'SELECT clear_all_active_checkpoints_for_app(@app_id)',
      parameters: {'app_id': appId},
      completer: completer,
    ));
    await completer.future;
    await _refreshCheckpointsCache();
  }

  Future<void> _deactivateCheckpoint(int checkpointId, int appId) async {
    final completer = Completer<void>();
    _dbOperationController.add(DatabaseOperation(
      query: 'SELECT deactivate_checkpoint(@checkpoint_id, @app_id)',
      parameters: {'checkpoint_id': checkpointId, 'app_id': appId},
      completer: completer,
    ));
    await completer.future;
    _activeCheckpointIds.remove(checkpointId);
    await _refreshCheckpointsCache();
    _trackingStatusController.add(_trackingStatus.copyWith(
      activeCheckpointIds: _activeCheckpointIds,
    ));
  }

  Future<void> _activateCheckpoint(int checkpointId, int appId) async {
    final completer = Completer<void>();
    _dbOperationController.add(DatabaseOperation(
      query: 'SELECT activate_checkpoint(@checkpoint_id, @app_id)',
      parameters: {'checkpoint_id': checkpointId, 'app_id': appId},
      completer: completer,
    ));
    await completer.future;
    _activeCheckpointIds.add(checkpointId);
    await _refreshCheckpointsCache();
    _trackingStatusController.add(_trackingStatus.copyWith(
      activeCheckpointIds: _activeCheckpointIds,
    ));
  }
}

Future<void> main(List<String> arguments) async {
  final parser = ArgParser()
    ..addFlag('display', help: 'Display data for process and return')
    ..addFlag('display-all', help: 'Display data for all processes and return')
    ..addFlag('insert', help: 'Insert process if it is not already tracked')
    ..addFlag('debug', help: 'Print debug output')
    ..addFlag('pause', help: 'Pause/Unpause tracking')
    ..addFlag('help', abbr: 'h', help: 'Show this help message');

  late final ArgResults results;
  try {
    results = parser.parse(arguments);
  } catch (e) {
    print('Error: $e');
    print(parser.usage);
    exit(1);
  }

  if (results['help'] as bool) {
    print('Time Tracker - A small script to interface with TimeTracker.');
    print(parser.usage);
    exit(0);
  }

  final timeTracker = TimeTracker();
  await timeTracker._initDatabase();

  if (results['pause'] as bool) {
    print('Pause functionality not implemented in simplified version');
    exit(0);
  }

  if (results['display-all'] as bool) {
    final apps = await timeTracker._getAllApps();
    print('Time data for all apps:\n');
    for (final app in apps) {
      print(app.toJson());
    }
    exit(0);
  }

  final remainingArgs = results.rest;
  if (results['display'] as bool) {
    if (remainingArgs.isEmpty) {
      print('Error: Process name required for display');
      exit(1);
    }

    final process = remainingArgs.first;
    final app = await timeTracker._getAppByName(process);

    if (app == null) {
      print('Could not find App: "$process".');
      exit(1);
    }

    print('Time data for app: "$process":\n');
    print(app.toJson());
    exit(0);
  }

  if (results['insert'] as bool) {
    if (remainingArgs.isEmpty) {
      print('Error: Process name required for insert');
      exit(1);
    }

    final process = remainingArgs.first;
    final existingApp = await timeTracker._getAppByName(process);

    if (existingApp == null) {
      await timeTracker._insertNewApp(process);
      print('App: "$process" has been inserted.');
    } else {
      print('App: "$process" already exists.');
    }
    exit(0);
  }

  // Check for existing instances
  final result = await Process.run('pgrep', ['time-tracker']);
  final instances = result.stdout.toString().trim().split('\n');
  final currentPid = pid.toString();

  if (instances.any((instance) => instance != currentPid && instance.isNotEmpty)) {
    print('There is already an instance of "time-tracker" running. Closing...');
    exit(0);
  }

  await timeTracker._initializeProcesses();
  await timeTracker.startDiscovery(debug: results['debug'] as bool);
}