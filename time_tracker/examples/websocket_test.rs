use std::sync::{Arc, RwLock};
use std::thread;
use std::time::Duration;
use time_tracker::structs::TrackingStatus;
use time_tracker::web_socket::{init_web_socket, update_tracking_status};
use time_tracker::test_client::TestClient;
use time_tracker::time_tracking::TimeTrackingConfig;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    env_logger::init();
    
    println!("Starting WebSocket broadcasting test...");
    
    // Create a test client
    let rt = tokio::runtime::Runtime::new()?;
    let test_client = rt.block_on(async {
        TestClient::new("sqlite::memory:").await
    })?;
    
    // Initialize WebSocket server with broadcasting
    let config = Arc::new(RwLock::new(TimeTrackingConfig::default()));
    let websocket_state = init_web_socket(test_client, config);
    
    println!("WebSocket server started on 127.0.0.1:6754");
    println!("You can connect multiple WebSocket clients to test broadcasting.");
    println!("The server will send tracking status updates every 5 seconds.");
    
    // Simulate tracking status updates
    let websocket_state_clone = Arc::clone(&websocket_state);
    thread::spawn(move || {
        let mut counter = 0;
        loop {
            thread::sleep(Duration::from_secs(5));
            counter += 1;
            
            let status = if counter % 2 == 1 {
                TrackingStatus {
                    is_tracking: true,
                    is_paused: false,
                    current_app: Some(format!("TestApp{}", counter)),
                    current_session_duration: counter * 60,
                    session_start_time: Some(chrono::Utc::now().format("%Y-%m-%dT%H:%M:%S%.fZ").to_string()),
                    active_checkpoint_ids: vec![1, 2],
                }
            } else {
                TrackingStatus {
                    is_tracking: false,
                    is_paused: false,
                    current_app: None,
                    current_session_duration: 0,
                    session_start_time: None,
                    active_checkpoint_ids: vec![],
                }
            };
            
            println!("Broadcasting tracking status update: is_tracking={}", status.is_tracking);
            update_tracking_status(&websocket_state_clone, status);
        }
    });
    
    println!("Press Ctrl+C to stop the server");
    
    // Keep the main thread alive
    loop {
        thread::sleep(Duration::from_secs(1));
    }
}
