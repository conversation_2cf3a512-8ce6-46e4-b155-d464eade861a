use std::thread;
use std::time::Duration;
use tungstenite::{connect, Message};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Connecting to WebSocket server...");

    let (mut socket, response) = connect("ws://127.0.0.1:6754")?;
    println!("Connected to the server");
    println!("Response HTTP code: {}", response.status());

    // Send a get_tracking_status command to get initial status
    let get_status_command = r#"{"type": "get_tracking_status", "payload": ""}"#;
    socket.send(Message::Text(get_status_command.into()))?;
    println!("Sent get_tracking_status command");
    
    // Listen for messages
    loop {
        match socket.read() {
            Ok(msg) => {
                if msg.is_text() || msg.is_binary() {
                    println!("Received: {}", msg.to_text()?);
                }
            }
            Err(e) => {
                println!("Error reading message: {}", e);
                break;
            }
        }
        
        // Small delay to prevent busy waiting
        thread::sleep(Duration::from_millis(100));
    }
    
    Ok(())
}
