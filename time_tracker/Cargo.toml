[package]
name = "time_tracker"
version = "0.1.0"
authors = ["<PERSON> <ka<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>"]
edition = "2024"
build = "build.rs"

[dependencies]
libc = "0.2.66"
reqwest = { version = "0.9.24", optional = true }
serde_json = "1.0.44"
serde = { version = "1.0.104", features = ["derive"] }
crossbeam-channel = "0.5.0"
chrono = { version = "0.4.41", features = ["serde"] }
log = "0.4.8"
env_logger = "0.7.1"
jsonrpc-core = "14.0.5"
jsonrpc-http-server = "14.0.5"
lazy_static = "1.5.0"
# postgres = { version = "0.15.2", features = ["with-chrono"], optional = true }
inputbot = "0.4.0"
rodio = { version = "0.21.1", default-features = false, features = ["playback"] }
# barrel = { version = "0.6.5", features = ["pg"], optional = true }
toml = "0.5.6"
serde_derive = "1.0.219"
rust-embed = "8.7.2"
tungstenite = "0.27.0"
futures-util = "0.3.31"
async-trait = "0.1"

# SeaORM dependencies
sea-orm = { version = "0.12", features = [ "sqlx-postgres", "sqlx-sqlite", "runtime-tokio-rustls", "macros" ], optional = true }
sea-orm-migration = { version = "0.12", optional = true }

[target.'cfg(not(windows))'.dependencies]
zbus = { version = "3.14", default-features = false, features = ["tokio"] }
tokio = { version = "1.47.1", features = ["rt", "rt-multi-thread", "test-util"] }
procfs = "0.15"
dirs = "5.0"
sysinfo = "0.37.0"
niri-ipc = "25.5.1"

[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3.8", features = ["tlhelp32", "handleapi", "winver", "winuser", "wincon", "shellapi"] }
wio = "0.2.2"
regex = "1.3.1"
systray = "0.3.0"

[target.'cfg(windows)'.build-dependencies]
embed-resource = "1.3.2"

[features]
firebase = ["reqwest"]
psql = ["sea-orm", "sea-orm-migration"]
memory = ["sea-orm", "sea-orm-migration"]
